# 🚀 Instalación y Configuración - FOFA Channel Manager

## 📋 Archivos incluidos

```
fofa7/
├── list.php              # ⭐ Script principal (USAR ESTE)
├── demo.php              # 🎬 Versión demo con datos simulados
├── auto_update.php       # 🔄 Auto-actualización cada 30 minutos
├── test_local.php        # 🧪 Pruebas locales
├── test.php              # 🔍 Verificación del sistema
├── setup_cron.sh         # ⚙️ Configurador de cron job
├── cache/                # 💾 Directorio de cache (se crea automáticamente)
└── README.md             # 📖 Documentación completa
```

## 🌐 Instalación en Hosting

### Paso 1: Subir archivos
1. Subir todos los archivos a tu hosting en la carpeta deseada
2. Ejemplo: `public_html/mamones/fofa7/`

### Paso 2: Verificar permisos
1. Asegurar que el directorio tenga permisos de escritura (755 o 777)
2. El script creará automáticamente la carpeta `cache/`

### Paso 3: Probar instalación
Visitar: `http://tudominio.com/mamones/fofa7/test.php`

## 🎯 URLs de Uso

### Script Principal (PRODUCCIÓN)
```
# Página principal
http://rogsmediatv.xyz/mamones/fofa7/list.php

# Listar canales por país
http://rogsmediatv.xyz/mamones/fofa7/list.php?US    # Estados Unidos
http://rogsmediatv.xyz/mamones/fofa7/list.php?CR    # Costa Rica
http://rogsmediatv.xyz/mamones/fofa7/list.php?GT    # Guatemala
http://rogsmediatv.xyz/mamones/fofa7/list.php?MX    # México

# Obtener canal específico
http://rogsmediatv.xyz/mamones/fofa7/list.php?CR=Repretel 6
http://rogsmediatv.xyz/mamones/fofa7/list.php?US=ESPN HD
```

### Script Demo (PRUEBAS)
```
# Para probar sin usar API real de FOFA
http://rogsmediatv.xyz/mamones/fofa7/demo.php
http://rogsmediatv.xyz/mamones/fofa7/demo.php?CR
http://rogsmediatv.xyz/mamones/fofa7/demo.php?CR=Repretel 6
```

## 🔧 Configuración Avanzada

### Auto-actualización (Opcional)
Para configurar la verificación automática cada 30 minutos:

```bash
# En el servidor (SSH)
cd /path/to/fofa7/
chmod +x setup_cron.sh
./setup_cron.sh
```

O manualmente agregar a crontab:
```bash
*/30 * * * * /usr/bin/php /path/to/fofa7/auto_update.php
```

### Verificación manual
```
http://rogsmediatv.xyz/mamones/fofa7/auto_update.php?auto_update=1
```

## 🌍 Países Soportados (LATAM Completo)

### Centroamérica y Caribe
| Código | País | Ejemplo | Canales Típicos |
|--------|------|---------|-----------------|
| `GT` | Guatemala | `?GT` | Canal 3, Guatevision |
| `BZ` | Belize | `?BZ` | Channel 5, Channel 7 |
| `SV` | El Salvador | `?SV` | Canal 2, Canal 4 |
| `HN` | **Honduras** | `?HN` | **TeleVicentro, HCH, Canal 5** |
| `NI` | Nicaragua | `?NI` | Canal 2, Canal 10 |
| `CR` | Costa Rica | `?CR` | Repretel 6, Canal 7, Teletica |
| `PA` | Panamá | `?PA` | TVN, RPC |
| `DO` | Rep. Dominicana | `?DO` | Antena 7, Telesistema |
| `PR` | Puerto Rico | `?PR` | Wapa TV, Univision |

### Sudamérica
| Código | País | Ejemplo | Canales Típicos |
|--------|------|---------|-----------------|
| `CO` | Colombia | `?CO` | RCN, Caracol |
| `VE` | Venezuela | `?VE` | Venevision, Televen |
| `BR` | Brasil | `?BR` | Globo, SBT |
| `EC` | Ecuador | `?EC` | Ecuavisa, TC Television |
| `PE` | Perú | `?PE` | America TV, Latina |
| `BO` | Bolivia | `?BO` | Unitel, ATB |
| `PY` | Paraguay | `?PY` | SNT, Telefuturo |
| `AR` | Argentina | `?AR` | Canal 13, Telefe |
| `UY` | Uruguay | `?UY` | Canal 10, Teledoce |
| `CL` | Chile | `?CL` | TVN, Canal 13 |

### Norteamérica
| Código | País | Ejemplo | Canales Típicos |
|--------|------|---------|-----------------|
| `MX` | México | `?MX` | Televisa, TV Azteca |
| `US` | Estados Unidos | `?US` | ESPN, CNN, Discovery |
| `CA` | Canadá | `?CA` | CBC, CTV |

## 📊 Respuestas del API

### Listar canales
```json
{
    "country": "CR",
    "channels": ["Repretel 6", "Canal 7", "Teletica"],
    "total": 3,
    "last_update": "2025-01-28 15:30:00"
}
```

### Canal específico
- **Éxito**: Redirección HTTP 302 al stream
- **No encontrado**: HTTP 404 + mensaje de error
- **No disponible**: HTTP 503 + mensaje temporal

## 🔍 Troubleshooting

### ❌ Error: "País no soportado"
- Verificar que uses códigos válidos (US, CR, GT, etc.)

### ❌ Error: "Canal no encontrado"
- Listar canales disponibles primero: `?CR`
- Usar nombres exactos o parciales

### ❌ Error: "Sin permisos de escritura"
- Cambiar permisos del directorio: `chmod 755 fofa7/`

### ❌ Error: "API FOFA sin saldo"
- Verificar saldo en cuenta FOFA
- Usar `demo.php` para pruebas

### ❌ Cache vacío
- Ejecutar: `auto_update.php?auto_update=1`
- Verificar conectividad con FOFA

## 🔐 Seguridad

### API Key FOFA
La API key está incluida en el código. Para mayor seguridad:

1. Crear archivo `config.php`:
```php
<?php
return [
    'fofa_key' => 'TU_API_KEY_AQUI'
];
?>
```

2. Modificar `list.php` línea 14:
```php
private $fofa_key;

public function __construct() {
    $config = include 'config.php';
    $this->fofa_key = $config['fofa_key'];
    // ... resto del código
}
```

## 📈 Monitoreo

### Logs
- Auto-actualización: `cache/auto_update.log`
- Errores PHP: Logs del servidor web

### Estadísticas
- Verificar archivos en `cache/channels_*.json`
- Revisar timestamps de última actualización

## 🚀 Puesta en Producción

1. ✅ Subir archivos al hosting
2. ✅ Probar con `test.php`
3. ✅ Verificar con `demo.php`
4. ✅ Configurar cron job (opcional)
5. ✅ Probar URLs reales
6. ✅ Monitorear logs

## 📞 Soporte

El script es **completamente portable** y funciona en cualquier hosting con:
- PHP 7.0+
- Permisos de escritura
- Conectividad HTTPS (para API FOFA)

**¡Listo para usar!** 🎉
