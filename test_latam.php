<?php
/**
 * Prueba masiva de todos los países de LATAM
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🌎 Prueba LATAM - Todos los Países</h1>";

// Incluir el demo
require_once 'demo.php';

$countries_to_test = [
    // Centroamérica
    'GT' => 'Guatemala',
    'BZ' => 'Belize', 
    'SV' => 'El Salvador',
    'HN' => 'Honduras',
    'NI' => 'Nicaragua',
    'CR' => 'Costa Rica',
    'PA' => 'Panama',
    
    // Caribe
    'DO' => 'Dominican Republic',
    'PR' => 'Puerto Rico',
    
    // Sudamérica
    'CO' => 'Colombia',
    'VE' => 'Venezuela',
    'BR' => 'Brazil',
    'EC' => 'Ecuador',
    'PE' => 'Peru',
    'BO' => 'Bolivia',
    'PY' => 'Paraguay',
    'AR' => 'Argentina',
    'UY' => 'Uruguay',
    'CL' => 'Chile',
    
    // Norteamérica
    'MX' => 'Mexico',
    'US' => 'United States'
];

echo "<h2>📊 Resumen de canales por país</h2>";
echo "<table border='1' cellpadding='8' cellspacing='0' style='border-collapse: collapse;'>";
echo "<tr style='background-color: #f0f0f0;'>";
echo "<th>País</th><th>Código</th><th>Canales Encontrados</th><th>Total</th><th>Ejemplo de Canal</th>";
echo "</tr>";

$total_countries = 0;
$total_channels = 0;

foreach ($countries_to_test as $code => $name) {
    // Simular la consulta
    $_SERVER['QUERY_STRING'] = $code;
    
    // Capturar la salida
    ob_start();
    $manager = new FOFAChannelManagerDemo();
    $manager->handleRequest();
    $output = ob_get_clean();
    
    // Decodificar JSON
    $data = json_decode($output, true);
    
    if ($data && isset($data['channels'])) {
        $channels = $data['channels'];
        $channel_count = count($channels);
        $example_channel = $channel_count > 0 ? $channels[0] : 'N/A';
        
        $total_countries++;
        $total_channels += $channel_count;
        
        // Color de fila basado en cantidad de canales
        $row_color = '';
        if ($channel_count == 0) {
            $row_color = 'background-color: #ffebee;'; // Rojo claro
        } elseif ($channel_count <= 2) {
            $row_color = 'background-color: #fff3e0;'; // Naranja claro
        } elseif ($channel_count <= 4) {
            $row_color = 'background-color: #f3e5f5;'; // Púrpura claro
        } else {
            $row_color = 'background-color: #e8f5e8;'; // Verde claro
        }
        
        echo "<tr style='$row_color'>";
        echo "<td><strong>$name</strong></td>";
        echo "<td><code>$code</code></td>";
        echo "<td>" . implode(', ', array_slice($channels, 0, 3));
        if ($channel_count > 3) {
            echo " <em>(+" . ($channel_count - 3) . " más)</em>";
        }
        echo "</td>";
        echo "<td><strong>$channel_count</strong></td>";
        echo "<td><code>$example_channel</code></td>";
        echo "</tr>";
    } else {
        echo "<tr style='background-color: #ffebee;'>";
        echo "<td><strong>$name</strong></td>";
        echo "<td><code>$code</code></td>";
        echo "<td colspan='3'>❌ Sin datos disponibles</td>";
        echo "</tr>";
    }
}

echo "</table>";

echo "<h2>📈 Estadísticas Generales</h2>";
echo "<ul>";
echo "<li><strong>Países analizados:</strong> $total_countries</li>";
echo "<li><strong>Total de canales:</strong> $total_channels</li>";
echo "<li><strong>Promedio por país:</strong> " . round($total_channels / max($total_countries, 1), 1) . " canales</li>";
echo "</ul>";

echo "<h2>🎯 Países con más canales (TOP 5)</h2>";

// Obtener datos para ranking
$country_data = [];
foreach ($countries_to_test as $code => $name) {
    $_SERVER['QUERY_STRING'] = $code;
    ob_start();
    $manager = new FOFAChannelManagerDemo();
    $manager->handleRequest();
    $output = ob_get_clean();
    $data = json_decode($output, true);
    
    if ($data && isset($data['channels'])) {
        $country_data[] = [
            'name' => $name,
            'code' => $code,
            'count' => count($data['channels']),
            'channels' => $data['channels']
        ];
    }
}

// Ordenar por cantidad de canales
usort($country_data, function($a, $b) {
    return $b['count'] - $a['count'];
});

echo "<ol>";
foreach (array_slice($country_data, 0, 5) as $country) {
    echo "<li><strong>{$country['name']} ({$country['code']})</strong> - {$country['count']} canales</li>";
}
echo "</ol>";

echo "<h2>🔗 Enlaces de prueba para Honduras</h2>";
echo "<p>Ya que preguntaste específicamente por Honduras, aquí tienes algunos enlaces de prueba:</p>";
echo "<ul>";
echo "<li><a href='demo.php?HN' target='_blank'>Listar todos los canales de Honduras</a></li>";
echo "<li><a href='demo.php?HN=TeleVicentro' target='_blank'>Canal TeleVicentro</a></li>";
echo "<li><a href='demo.php?HN=HCH' target='_blank'>Canal HCH</a></li>";
echo "<li><a href='demo.php?HN=Canal 5 Honduras' target='_blank'>Canal 5 Honduras</a></li>";
echo "</ul>";

echo "<h2>🌎 Enlaces para otros países centroamericanos</h2>";
$central_america = ['GT' => 'Guatemala', 'SV' => 'El Salvador', 'NI' => 'Nicaragua', 'CR' => 'Costa Rica', 'PA' => 'Panama'];

echo "<table border='1' cellpadding='5'>";
echo "<tr><th>País</th><th>Listar Canales</th><th>Canal de Ejemplo</th></tr>";

foreach ($central_america as $code => $name) {
    // Obtener primer canal para ejemplo
    $_SERVER['QUERY_STRING'] = $code;
    ob_start();
    $manager = new FOFAChannelManagerDemo();
    $manager->handleRequest();
    $output = ob_get_clean();
    $data = json_decode($output, true);
    
    $example_channel = '';
    if ($data && isset($data['channels']) && count($data['channels']) > 0) {
        $example_channel = $data['channels'][0];
    }
    
    echo "<tr>";
    echo "<td><strong>$name</strong></td>";
    echo "<td><a href='demo.php?$code' target='_blank'>demo.php?$code</a></td>";
    if ($example_channel) {
        echo "<td><a href='demo.php?$code=" . urlencode($example_channel) . "' target='_blank'>$example_channel</a></td>";
    } else {
        echo "<td>Sin canales</td>";
    }
    echo "</tr>";
}

echo "</table>";

echo "<h2>⚠️ Nota Importante</h2>";
echo "<p><strong>Estos son datos de DEMOSTRACIÓN.</strong> En el script real (<code>list.php</code>), los datos se obtienen directamente de la API de FOFA buscando servidores Astra reales en cada país.</p>";

echo "<h2>🚀 Para usar en producción</h2>";
echo "<p>Una vez que tengas una API key de FOFA con saldo suficiente, simplemente usa:</p>";
echo "<ul>";
echo "<li><code>list.php?HN</code> - Para listar canales reales de Honduras</li>";
echo "<li><code>list.php?HN=nombre_canal</code> - Para obtener un canal específico</li>";
echo "</ul>";

echo "<p><em>Generado el: " . date('Y-m-d H:i:s') . "</em></p>";
?>
