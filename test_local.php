<?php
/**
 * Script de prueba local para verificar funcionamiento
 * Simula el entorno de hosting
 */

// Simular variables de servidor para prueba local
if (!isset($_SERVER['HTTP_HOST'])) {
    $_SERVER['HTTP_HOST'] = 'localhost';
    $_SERVER['SCRIPT_NAME'] = '/fofa7/list.php';
    $_SERVER['REQUEST_URI'] = '/fofa7/test_local.php';
}

echo "<h1>🧪 FOFA Channel Manager - Prueba Local</h1>";

// Información del entorno
echo "<h2>📍 Información del entorno</h2>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><td><strong>HTTP_HOST</strong></td><td>" . ($_SERVER['HTTP_HOST'] ?? 'No definido') . "</td></tr>";
echo "<tr><td><strong>SCRIPT_NAME</strong></td><td>" . ($_SERVER['SCRIPT_NAME'] ?? 'No definido') . "</td></tr>";
echo "<tr><td><strong>REQUEST_URI</strong></td><td>" . ($_SERVER['REQUEST_URI'] ?? 'No definido') . "</td></tr>";
echo "<tr><td><strong>DOCUMENT_ROOT</strong></td><td>" . ($_SERVER['DOCUMENT_ROOT'] ?? 'No definido') . "</td></tr>";
echo "<tr><td><strong>__FILE__</strong></td><td>" . __FILE__ . "</td></tr>";
echo "<tr><td><strong>dirname(__FILE__)</strong></td><td>" . dirname(__FILE__) . "</td></tr>";
echo "</table>";

// Verificar archivos necesarios
echo "<h2>📁 Verificación de archivos</h2>";
$required_files = ['list.php', 'auto_update.php'];
foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "✅ <strong>$file</strong> - " . number_format(filesize($file)) . " bytes<br>";
    } else {
        echo "❌ <strong>$file</strong> - No encontrado<br>";
    }
}

// Verificar directorio cache
echo "<h2>💾 Verificación de cache</h2>";
$cache_dir = dirname(__FILE__) . '/cache/';
if (!file_exists($cache_dir)) {
    if (mkdir($cache_dir, 0755, true)) {
        echo "✅ Directorio cache creado: <code>$cache_dir</code><br>";
    } else {
        echo "❌ No se pudo crear directorio cache<br>";
    }
} else {
    echo "✅ Directorio cache existe: <code>$cache_dir</code><br>";
}

if (is_writable($cache_dir)) {
    echo "✅ Directorio cache es escribible<br>";
} else {
    echo "❌ Directorio cache NO es escribible<br>";
}

// Probar la clase principal
echo "<h2>🔧 Prueba de la clase FOFAChannelManager</h2>";
try {
    require_once 'list.php';
    echo "✅ Archivo list.php cargado correctamente<br>";
    
    $manager = new FOFAChannelManager();
    echo "✅ Clase FOFAChannelManager instanciada<br>";
    
    // Usar reflexión para acceder a propiedades privadas
    $reflection = new ReflectionClass($manager);
    
    $base_url_prop = $reflection->getProperty('base_url');
    $base_url_prop->setAccessible(true);
    $base_url = $base_url_prop->getValue($manager);
    echo "✅ URL Base detectada: <code>$base_url</code><br>";
    
    $script_path_prop = $reflection->getProperty('script_path');
    $script_path_prop->setAccessible(true);
    $script_path = $script_path_prop->getValue($manager);
    echo "✅ Ruta del script: <code>$script_path</code><br>";
    
    $cache_dir_prop = $reflection->getProperty('cache_dir');
    $cache_dir_prop->setAccessible(true);
    $cache_dir_detected = $cache_dir_prop->getValue($manager);
    echo "✅ Directorio cache detectado: <code>$cache_dir_detected</code><br>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

// Probar conectividad con FOFA
echo "<h2>🌐 Prueba de conectividad con FOFA</h2>";
$fofa_key = '62b20e1e01319d0c005c580fbaf8c22f';
$test_query = base64_encode('"test"');
$fofa_url = "https://fofa.info/api/v1/search/all?key={$fofa_key}&qbase64={$test_query}&size=1";

echo "URL de prueba: <code>" . htmlspecialchars($fofa_url) . "</code><br>";

$context = stream_context_create([
    'http' => [
        'timeout' => 10,
        'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    ]
]);

$start_time = microtime(true);
$response = @file_get_contents($fofa_url, false, $context);
$end_time = microtime(true);
$response_time = round(($end_time - $start_time) * 1000, 2);

if ($response === false) {
    echo "❌ No se pudo conectar con FOFA<br>";
    $error = error_get_last();
    if ($error) {
        echo "Error: " . htmlspecialchars($error['message']) . "<br>";
    }
} else {
    echo "✅ Conectividad con FOFA OK (Tiempo: {$response_time}ms)<br>";
    $data = json_decode($response, true);
    if ($data && isset($data['results'])) {
        echo "✅ Respuesta JSON válida<br>";
        echo "Resultados de prueba: " . count($data['results']) . "<br>";
        if (isset($data['size'])) {
            echo "Tamaño de respuesta: " . $data['size'] . "<br>";
        }
    } else {
        echo "❌ Respuesta JSON inválida<br>";
        echo "Respuesta (primeros 200 chars): " . htmlspecialchars(substr($response, 0, 200)) . "...<br>";
    }
}

// Simular una consulta real
echo "<h2>🎯 Prueba de consulta real (Costa Rica)</h2>";
$real_query = '"Astra Control Panel" && country="CR" && icon_hash="-463666563"';
$real_query_b64 = base64_encode($real_query);
$real_fofa_url = "https://fofa.info/api/v1/search/all?key={$fofa_key}&qbase64={$real_query_b64}&size=5";

echo "Consulta: <code>" . htmlspecialchars($real_query) . "</code><br>";
echo "URL: <code>" . htmlspecialchars($real_fofa_url) . "</code><br>";

$start_time = microtime(true);
$real_response = @file_get_contents($real_fofa_url, false, $context);
$end_time = microtime(true);
$real_response_time = round(($end_time - $start_time) * 1000, 2);

if ($real_response === false) {
    echo "❌ No se pudo obtener datos de Costa Rica<br>";
} else {
    echo "✅ Datos de Costa Rica obtenidos (Tiempo: {$real_response_time}ms)<br>";
    $real_data = json_decode($real_response, true);
    if ($real_data && isset($real_data['results'])) {
        echo "✅ Servidores Astra encontrados: " . count($real_data['results']) . "<br>";
        if (count($real_data['results']) > 0) {
            echo "<strong>Ejemplo de servidor:</strong><br>";
            $first_server = $real_data['results'][0];
            echo "IP: " . htmlspecialchars($first_server[0]) . "<br>";
            if (isset($first_server[1])) {
                echo "Puerto: " . htmlspecialchars($first_server[1]) . "<br>";
            }
        }
    }
}

// Enlaces de prueba
echo "<h2>🔗 Enlaces de prueba</h2>";
$current_dir = dirname($_SERVER['REQUEST_URI']);
echo "<ul>";
echo "<li><a href='list.php' target='_blank'>Página principal del script</a></li>";
echo "<li><a href='list.php?CR' target='_blank'>Canales de Costa Rica</a></li>";
echo "<li><a href='list.php?US' target='_blank'>Canales de Estados Unidos</a></li>";
echo "<li><a href='auto_update.php?auto_update=1' target='_blank'>Ejecutar auto-actualización</a></li>";
echo "</ul>";

echo "<h2>✅ Resumen</h2>";
echo "<p>Si todas las verificaciones muestran ✅, el script está listo para funcionar en cualquier hosting.</p>";
echo "<p><strong>Próximo paso:</strong> Subir los archivos a tu hosting y probar con las URLs reales.</p>";
?>
