<?php
/**
 * Prueba con tu API key real de FOFA
 */

echo "🔑 PROBANDO TU API KEY DE FOFA\n";
echo "=============================\n\n";

$fofa_key = '62b20e1e01319d0c005c580fbaf8c22f';

// Primero probar con una consulta simple
echo "📡 Paso 1: Verificando conectividad básica...\n";
$test_query = base64_encode('title="test"');
$test_url = "https://fofa.info/api/v1/search/all?key={$fofa_key}&qbase64={$test_query}&size=1";

$context = stream_context_create([
    'http' => [
        'timeout' => 15,
        'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    ]
]);

$response = @file_get_contents($test_url, false, $context);

if ($response === false) {
    echo "❌ Error: No se puede conectar con FOFA\n";
    exit;
}

$data = json_decode($response, true);

if (!$data) {
    echo "❌ Error: Respuesta inválida\n";
    echo "Respuesta: " . substr($response, 0, 200) . "...\n";
    exit;
}

if (isset($data['error']) && $data['error']) {
    echo "❌ Error de FOFA: " . ($data['errmsg'] ?? 'Error desconocido') . "\n";
    
    // Verificar tipos de error comunes
    $error_msg = $data['errmsg'] ?? '';
    if (strpos($error_msg, '余额不足') !== false || strpos($error_msg, 'balance') !== false) {
        echo "\n💰 PROBLEMA: Tu cuenta FOFA no tiene saldo suficiente\n";
        echo "SOLUCIÓN: Necesitas agregar créditos a tu cuenta FOFA\n";
    } elseif (strpos($error_msg, 'key') !== false) {
        echo "\n🔑 PROBLEMA: API key inválida o expirada\n";
        echo "SOLUCIÓN: Verificar la API key en tu cuenta FOFA\n";
    } elseif (strpos($error_msg, 'limit') !== false) {
        echo "\n⏰ PROBLEMA: Límite de consultas excedido\n";
        echo "SOLUCIÓN: Esperar o upgradar tu plan FOFA\n";
    }
    exit;
}

echo "✅ Conectividad básica OK\n\n";

// Ahora probar la consulta específica para Honduras
echo "📡 Paso 2: Buscando servidores Astra en Honduras...\n";
$honduras_query = '"Astra Control Panel" && country="HN" && icon_hash="-463666563"';
$honduras_query_b64 = base64_encode($honduras_query);
$honduras_url = "https://fofa.info/api/v1/search/all?key={$fofa_key}&qbase64={$honduras_query_b64}&size=10";

echo "Consulta: {$honduras_query}\n";

$start_time = microtime(true);
$honduras_response = @file_get_contents($honduras_url, false, $context);
$end_time = microtime(true);
$response_time = round(($end_time - $start_time) * 1000, 2);

if ($honduras_response === false) {
    echo "❌ Error en consulta de Honduras\n";
    exit;
}

$honduras_data = json_decode($honduras_response, true);

if (!$honduras_data) {
    echo "❌ Respuesta inválida para Honduras\n";
    exit;
}

if (isset($honduras_data['error']) && $honduras_data['error']) {
    echo "❌ Error en consulta Honduras: " . ($honduras_data['errmsg'] ?? 'Error desconocido') . "\n";
    exit;
}

echo "✅ Consulta Honduras exitosa (Tiempo: {$response_time}ms)\n\n";

// Mostrar resultados
$results = $honduras_data['results'] ?? [];
$total = count($results);

echo "📊 RESULTADOS PARA HONDURAS:\n";
echo "----------------------------\n";
echo "Servidores Astra encontrados: {$total}\n\n";

if ($total === 0) {
    echo "⚠️  No se encontraron servidores Astra en Honduras\n";
    echo "Esto puede significar:\n";
    echo "• No hay servidores Astra activos en Honduras actualmente\n";
    echo "• Los servidores no están indexados en FOFA\n";
    echo "• Necesitas probar con otros criterios de búsqueda\n\n";
    
    echo "💡 SUGERENCIAS:\n";
    echo "• Probar con otros países: GT, CR, SV, NI\n";
    echo "• Usar el modo demo mientras tanto\n";
} else {
    echo "🎉 ¡EXCELENTE! Se encontraron servidores en Honduras:\n\n";
    
    foreach (array_slice($results, 0, 5) as $i => $server) {
        $ip = $server[0] ?? 'N/A';
        $port = $server[1] ?? 'N/A';
        $protocol = $server[2] ?? 'N/A';
        
        echo ($i + 1) . ". IP: {$ip}:{$port} ({$protocol})\n";
    }
    
    if ($total > 5) {
        echo "   ... y " . ($total - 5) . " servidores más\n";
    }
}

echo "\n🔍 INFORMACIÓN DE TU CUENTA FOFA:\n";
echo "--------------------------------\n";
echo "API Key: " . substr($fofa_key, 0, 8) . "..." . substr($fofa_key, -4) . "\n";
echo "Estado: " . ($total >= 0 ? "✅ Funcionando" : "❌ Con problemas") . "\n";

if (isset($honduras_data['size'])) {
    echo "Total disponible en FOFA: " . $honduras_data['size'] . "\n";
}

echo "\n🚀 PRÓXIMOS PASOS:\n";
echo "-----------------\n";
if ($total > 0) {
    echo "1. ✅ Tu API key funciona perfectamente\n";
    echo "2. ✅ Hay servidores Astra en Honduras\n";
    echo "3. 🚀 Subir archivos al hosting y usar:\n";
    echo "   http://rogsmediatv.xyz/mamones/fofa7/list.php?HN\n";
} else {
    echo "1. ✅ Tu API key funciona\n";
    echo "2. ⚠️  No hay servidores Astra en Honduras actualmente\n";
    echo "3. 🔄 Probar con otros países o usar modo demo\n";
}

echo "\nFecha: " . date('Y-m-d H:i:s') . "\n";
?>
