<?php
/**
 * Script de demostración con datos simulados
 * Para probar el funcionamiento sin usar la API real de FOFA
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);
set_time_limit(300);

class FOFAChannelManagerDemo {
    private $cache_dir;
    private $cache_duration = 1800; // 30 minutos
    private $base_url;
    private $script_path;
    private $countries = [
        // Norteamérica
        'US' => 'United States',
        'CA' => 'Canada',
        'MX' => 'Mexico',

        // Centroamérica y Caribe
        'GT' => 'Guatemala',
        'BZ' => 'Belize',
        'SV' => 'El Salvador',
        'HN' => 'Honduras',
        'NI' => 'Nicaragua',
        'CR' => 'Costa Rica',
        'PA' => 'Panama',
        'DO' => 'Dominican Republic',
        'PR' => 'Puerto Rico',

        // Sudamérica
        'CO' => 'Colombia',
        'VE' => 'Venezuela',
        'BR' => 'Brazil',
        'EC' => 'Ecuador',
        'PE' => 'Peru',
        'BO' => 'Bolivia',
        'PY' => 'Paraguay',
        'AR' => 'Argentina',
        'UY' => 'Uruguay',
        'CL' => 'Chile'
    ];
    
    // Datos simulados de servidores Astra
    private $demo_servers = [
        'US' => [
            ['*************', '8000'],
            ['*********', '8080'],
            ['************', '8000']
        ],
        'CR' => [
            ['201.192.45.100', '8000'],
            ['190.113.22.50', '8080']
        ],
        'GT' => [
            ['190.92.15.100', '8000'],
            ['201.159.223.45', '8080']
        ],
        'HN' => [
            ['190.53.46.100', '8000'],
            ['201.220.32.150', '8080'],
            ['186.151.198.75', '8000']
        ],
        'SV' => [
            ['168.243.200.100', '8000']
        ],
        'NI' => [
            ['200.107.58.100', '8000']
        ],
        'PA' => [
            ['190.34.199.100', '8000']
        ],
        'MX' => [
            ['187.141.30.100', '8000'],
            ['201.175.44.200', '8080']
        ],
        'CO' => [
            ['181.129.183.100', '8000'],
            ['190.90.160.200', '8080']
        ],
        'VE' => [
            ['200.109.236.100', '8000']
        ],
        'BR' => [
            ['177.67.82.100', '8000'],
            ['201.23.45.200', '8080']
        ],
        'AR' => [
            ['190.210.8.100', '8000']
        ],
        'CL' => [
            ['**************', '8000']
        ],
        'PE' => [
            ['***************', '8000']
        ],
        'EC' => [
            ['*************', '8000']
        ]
    ];
    
    // Playlists simuladas
    private $demo_playlists = [
        '*************:8000' => "#EXTM3U\n#EXTINF:-1,ESPN HD\nhttp://*************:8000/espn.m3u8\n#EXTINF:-1,CNN International\nhttp://*************:8000/cnn.m3u8\n#EXTINF:-1,Discovery Channel\nhttp://*************:8000/discovery.m3u8",
        '201.192.45.100:8000' => "#EXTM3U\n#EXTINF:-1,Repretel 6\nhttp://201.192.45.100:8000/repretel6.m3u8\n#EXTINF:-1,Canal 7\nhttp://201.192.45.100:8000/canal7.m3u8\n#EXTINF:-1,Teletica\nhttp://201.192.45.100:8000/teletica.m3u8",
        '190.92.15.100:8000' => "#EXTM3U\n#EXTINF:-1,Canal 3 Guatemala\nhttp://190.92.15.100:8000/canal3.m3u8\n#EXTINF:-1,Guatevision\nhttp://190.92.15.100:8000/guatevision.m3u8",
        '190.53.46.100:8000' => "#EXTM3U\n#EXTINF:-1,TeleVicentro\nhttp://190.53.46.100:8000/televicentro.m3u8\n#EXTINF:-1,Canal 5 Honduras\nhttp://190.53.46.100:8000/canal5.m3u8\n#EXTINF:-1,HCH\nhttp://190.53.46.100:8000/hch.m3u8\n#EXTINF:-1,TSi\nhttp://190.53.46.100:8000/tsi.m3u8",
        '201.220.32.150:8080' => "#EXTM3U\n#EXTINF:-1,Canal 11 Honduras\nhttp://201.220.32.150:8080/canal11.m3u8\n#EXTINF:-1,TEN Canal 10\nhttp://201.220.32.150:8080/ten.m3u8",
        '168.243.200.100:8000' => "#EXTM3U\n#EXTINF:-1,Canal 2 El Salvador\nhttp://168.243.200.100:8000/canal2.m3u8\n#EXTINF:-1,Canal 4 El Salvador\nhttp://168.243.200.100:8000/canal4.m3u8",
        '200.107.58.100:8000' => "#EXTM3U\n#EXTINF:-1,Canal 2 Nicaragua\nhttp://200.107.58.100:8000/canal2ni.m3u8\n#EXTINF:-1,Canal 10 Nicaragua\nhttp://200.107.58.100:8000/canal10ni.m3u8",
        '190.34.199.100:8000' => "#EXTM3U\n#EXTINF:-1,TVN Panama\nhttp://190.34.199.100:8000/tvn.m3u8\n#EXTINF:-1,RPC Panama\nhttp://190.34.199.100:8000/rpc.m3u8",
        '187.141.30.100:8000' => "#EXTM3U\n#EXTINF:-1,Televisa\nhttp://187.141.30.100:8000/televisa.m3u8\n#EXTINF:-1,TV Azteca\nhttp://187.141.30.100:8000/azteca.m3u8",
        '181.129.183.100:8000' => "#EXTM3U\n#EXTINF:-1,RCN HD\nhttp://181.129.183.100:8000/rcnhd.m3u8\n#EXTINF:-1,Caracol\nhttp://181.129.183.100:8000/caracol.m3u8",
        '200.109.236.100:8000' => "#EXTM3U\n#EXTINF:-1,Venevision\nhttp://200.109.236.100:8000/venevision.m3u8\n#EXTINF:-1,Televen\nhttp://200.109.236.100:8000/televen.m3u8",
        '177.67.82.100:8000' => "#EXTM3U\n#EXTINF:-1,Globo\nhttp://177.67.82.100:8000/globo.m3u8\n#EXTINF:-1,SBT\nhttp://177.67.82.100:8000/sbt.m3u8",
        '190.210.8.100:8000' => "#EXTM3U\n#EXTINF:-1,Canal 13 Argentina\nhttp://190.210.8.100:8000/canal13.m3u8\n#EXTINF:-1,Telefe\nhttp://190.210.8.100:8000/telefe.m3u8",
        '**************:8000' => "#EXTM3U\n#EXTINF:-1,TVN Chile\nhttp://**************:8000/tvn.m3u8\n#EXTINF:-1,Canal 13 Chile\nhttp://**************:8000/canal13cl.m3u8",
        '***************:8000' => "#EXTM3U\n#EXTINF:-1,America TV Peru\nhttp://***************:8000/america.m3u8\n#EXTINF:-1,Latina Peru\nhttp://***************:8000/latina.m3u8",
        '*************:8000' => "#EXTM3U\n#EXTINF:-1,Ecuavisa\nhttp://*************:8000/ecuavisa.m3u8\n#EXTINF:-1,TC Television\nhttp://*************:8000/tc.m3u8"
    ];
    
    public function __construct() {
        $this->script_path = isset($_SERVER['SCRIPT_NAME']) ? dirname($_SERVER['SCRIPT_NAME']) : '';
        $this->cache_dir = dirname(__FILE__) . '/cache/';
        
        if (php_sapi_name() !== 'cli') {
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
            $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
            $this->base_url = $protocol . '://' . $host . $this->script_path;
        } else {
            $this->base_url = 'http://localhost' . $this->script_path;
        }
        
        if (!file_exists($this->cache_dir)) {
            mkdir($this->cache_dir, 0755, true);
        }
    }
    
    public function handleRequest() {
        $query_string = $_SERVER['QUERY_STRING'] ?? '';
        
        if (empty($query_string)) {
            $this->showUsage();
            return;
        }
        
        if (strpos($query_string, '=') !== false) {
            parse_str($query_string, $params);
            $country = key($params);
            $channel = $params[$country];
            $this->serveChannel($country, $channel);
        } else {
            $country = $query_string;
            $this->listChannels($country);
        }
    }
    
    private function showUsage() {
        header('Content-Type: text/html; charset=utf-8');
        $current_url = $this->base_url . '/demo.php';
        
        echo "<h1>🎬 FOFA Channel Manager - DEMO</h1>";
        echo "<p><strong>⚠️ MODO DEMOSTRACIÓN - Usando datos simulados</strong></p>";
        echo "<p><strong>URL Base:</strong> <code>{$current_url}</code></p>";
        echo "<h2>Uso:</h2>";
        echo "<ul>";
        foreach ($this->countries as $code => $name) {
            echo "<li><a href='?{$code}' target='_blank'>{$current_url}?{$code}</a> - Listar canales de {$name}</li>";
        }
        echo "</ul>";
        echo "<h2>Ejemplo de canal específico:</h2>";
        echo "<p><code>{$current_url}?CR=repretel6.m3u8</code></p>";
        echo "<p><code>{$current_url}?US=espn</code></p>";
        echo "<h2>Información del sistema:</h2>";
        echo "<ul>";
        echo "<li>Servidor: " . ($_SERVER['HTTP_HOST'] ?? 'CLI/Local') . "</li>";
        echo "<li>Ruta: " . $this->script_path . "</li>";
        echo "<li>Cache: " . (is_writable($this->cache_dir) ? '✅ Escribible' : '❌ Sin permisos') . "</li>";
        echo "<li>Hora actual: " . date('Y-m-d H:i:s') . "</li>";
        echo "<li>Modo: DEMO (datos simulados)</li>";
        echo "</ul>";
    }
    
    private function listChannels($country) {
        if (!isset($this->countries[$country])) {
            http_response_code(400);
            echo json_encode(['error' => "País no soportado: $country"]);
            return;
        }
        
        $channels = $this->getChannelsFromCache($country);
        
        if (empty($channels)) {
            $channels = $this->fetchChannelsDemo($country);
            if (!empty($channels)) {
                $this->saveChannelsToCache($country, $channels);
            }
        }
        
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'country' => $country,
            'country_name' => $this->countries[$country],
            'channels' => array_keys($channels),
            'total' => count($channels),
            'last_update' => date('Y-m-d H:i:s'),
            'mode' => 'DEMO',
            'servers_found' => count($this->demo_servers[$country] ?? [])
        ], JSON_PRETTY_PRINT);
    }
    
    private function serveChannel($country, $channel_name) {
        $channels = $this->getChannelsFromCache($country);
        
        if (empty($channels)) {
            $channels = $this->fetchChannelsDemo($country);
            if (!empty($channels)) {
                $this->saveChannelsToCache($country, $channels);
            }
        }
        
        // Buscar el canal
        $found_urls = [];
        foreach ($channels as $name => $urls) {
            if (stripos($name, $channel_name) !== false || $name === $channel_name) {
                $found_urls = $urls;
                break;
            }
        }
        
        if (empty($found_urls)) {
            http_response_code(404);
            echo json_encode([
                'error' => "Canal no encontrado: $channel_name",
                'available_channels' => array_keys($channels),
                'country' => $country
            ], JSON_PRETTY_PRINT);
            return;
        }
        
        // En modo demo, simplemente devolver la primera URL
        $url = $found_urls[0];
        
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'channel' => $channel_name,
            'country' => $country,
            'url' => $url,
            'backup_urls' => array_slice($found_urls, 1),
            'mode' => 'DEMO',
            'note' => 'En producción, esto redirigiría al stream'
        ], JSON_PRETTY_PRINT);
    }
    
    private function fetchChannelsDemo($country) {
        if (!isset($this->demo_servers[$country])) {
            return [];
        }
        
        $channels = [];
        
        foreach ($this->demo_servers[$country] as $server) {
            $ip = $server[0];
            $port = $server[1];
            $server_key = "{$ip}:{$port}";
            
            if (isset($this->demo_playlists[$server_key])) {
                $playlist_content = $this->demo_playlists[$server_key];
                $extracted_channels = $this->parseM3UPlaylist($playlist_content, $ip, $port);
                $channels = array_merge_recursive($channels, $extracted_channels);
            }
        }
        
        return $channels;
    }
    
    private function parseM3UPlaylist($content, $ip, $port) {
        $channels = [];
        $lines = explode("\n", $content);
        $current_channel = null;
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            if (strpos($line, '#EXTINF:') === 0) {
                if (preg_match('/#EXTINF:.*?,(.*)/', $line, $matches)) {
                    $current_channel = trim($matches[1]);
                }
            } elseif (!empty($line) && strpos($line, '#') !== 0 && $current_channel) {
                if (!filter_var($line, FILTER_VALIDATE_URL)) {
                    $line = "http://{$ip}:{$port}/" . ltrim($line, '/');
                }
                
                if (!isset($channels[$current_channel])) {
                    $channels[$current_channel] = [];
                }
                $channels[$current_channel][] = $line;
                $current_channel = null;
            }
        }
        
        return $channels;
    }
    
    private function getChannelsFromCache($country) {
        $cache_file = $this->cache_dir . "demo_channels_{$country}.json";
        
        if (!file_exists($cache_file)) {
            return [];
        }
        
        $cache_time = filemtime($cache_file);
        if (time() - $cache_time > $this->cache_duration) {
            return [];
        }
        
        $content = file_get_contents($cache_file);
        return json_decode($content, true) ?: [];
    }
    
    private function saveChannelsToCache($country, $channels) {
        $cache_file = $this->cache_dir . "demo_channels_{$country}.json";
        file_put_contents($cache_file, json_encode($channels, JSON_PRETTY_PRINT));
    }
}

// Ejecutar el script
$manager = new FOFAChannelManagerDemo();
$manager->handleRequest();
?>
