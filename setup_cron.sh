#!/bin/bash

# Script para configurar cron job automático
# Ejecutar: chmod +x setup_cron.sh && ./setup_cron.sh

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CRON_JOB="*/30 * * * * /usr/bin/php $SCRIPT_DIR/auto_update.php"

echo "Configurando cron job para auto-actualización cada 30 minutos..."

# Agregar cron job si no existe
(crontab -l 2>/dev/null | grep -v "$SCRIPT_DIR/auto_update.php"; echo "$CRON_JOB") | crontab -

echo "Cron job configurado:"
echo "$CRON_JOB"
echo ""
echo "Para verificar: crontab -l"
echo "Para remover: crontab -e (y eliminar la línea correspondiente)"
