<?php
/**
 * Prueba específica para Honduras usando la API real de FOFA
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🇭🇳 Prueba FOFA - Honduras</h1>";

$fofa_key = '62b20e1e01319d0c005c580fbaf8c22f';
$country = 'HN'; // Honduras
$query = '"Astra Control Panel" && country="' . $country . '" && icon_hash="-463666563"';
$query_base64 = base64_encode($query);

$fofa_url = "https://fofa.info/api/v1/search/all?key={$fofa_key}&qbase64={$query_base64}&size=20";

echo "<h2>📋 Información de la consulta</h2>";
echo "<p><strong>País:</strong> Honduras (HN)</p>";
echo "<p><strong>Consulta:</strong> <code>" . htmlspecialchars($query) . "</code></p>";
echo "<p><strong>URL API:</strong> <code>" . htmlspecialchars($fofa_url) . "</code></p>";

echo "<h2>🔍 Ejecutando búsqueda...</h2>";

$context = stream_context_create([
    'http' => [
        'timeout' => 30,
        'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    ]
]);

$start_time = microtime(true);
$response = @file_get_contents($fofa_url, false, $context);
$end_time = microtime(true);
$response_time = round(($end_time - $start_time) * 1000, 2);

if ($response === false) {
    echo "❌ <strong>Error:</strong> No se pudo conectar con la API de FOFA<br>";
    $error = error_get_last();
    if ($error) {
        echo "Detalle del error: " . htmlspecialchars($error['message']) . "<br>";
    }
    exit;
}

echo "✅ <strong>Respuesta recibida</strong> (Tiempo: {$response_time}ms)<br>";

$data = json_decode($response, true);

if (!$data) {
    echo "❌ <strong>Error:</strong> Respuesta JSON inválida<br>";
    echo "Respuesta cruda: " . htmlspecialchars(substr($response, 0, 500)) . "...<br>";
    exit;
}

echo "<h2>📊 Resultados</h2>";

if (isset($data['error']) && $data['error']) {
    echo "❌ <strong>Error de FOFA:</strong> " . htmlspecialchars($data['errmsg'] ?? 'Error desconocido') . "<br>";
    
    // Verificar si es problema de saldo
    if (strpos($data['errmsg'] ?? '', '余额不足') !== false || strpos($data['errmsg'] ?? '', 'balance') !== false) {
        echo "<p>⚠️ <strong>Problema de saldo en la cuenta FOFA</strong></p>";
        echo "<p>Esto significa que la API key no tiene suficiente crédito para realizar búsquedas.</p>";
    }
    
    exit;
}

if (!isset($data['results'])) {
    echo "❌ <strong>Error:</strong> Formato de respuesta inesperado<br>";
    echo "Datos recibidos: <pre>" . htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT)) . "</pre>";
    exit;
}

$results = $data['results'];
$total_results = count($results);

echo "✅ <strong>Servidores Astra encontrados en Honduras:</strong> {$total_results}<br>";

if ($total_results === 0) {
    echo "<p>🔍 No se encontraron servidores Astra en Honduras con los criterios especificados.</p>";
    echo "<h3>💡 Sugerencias:</h3>";
    echo "<ul>";
    echo "<li>Probar con otros países de la región</li>";
    echo "<li>Verificar si hay servidores Astra activos en Honduras</li>";
    echo "<li>Intentar con diferentes criterios de búsqueda</li>";
    echo "</ul>";
} else {
    echo "<h3>🖥️ Servidores encontrados:</h3>";
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>#</th><th>IP</th><th>Puerto</th><th>Protocolo</th><th>Título</th><th>País</th></tr>";
    
    foreach ($results as $index => $server) {
        $ip = $server[0] ?? 'N/A';
        $port = $server[1] ?? 'N/A';
        $protocol = $server[2] ?? 'N/A';
        $title = $server[3] ?? 'N/A';
        $country_info = $server[4] ?? 'N/A';
        
        echo "<tr>";
        echo "<td>" . ($index + 1) . "</td>";
        echo "<td><code>{$ip}</code></td>";
        echo "<td><code>{$port}</code></td>";
        echo "<td>{$protocol}</td>";
        echo "<td>" . htmlspecialchars($title) . "</td>";
        echo "<td>{$country_info}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>🔗 Probando conectividad con servidores...</h3>";
    
    foreach (array_slice($results, 0, 3) as $index => $server) { // Solo probar los primeros 3
        $ip = $server[0];
        $port = $server[1] ?? '8000';
        
        echo "<h4>Servidor " . ($index + 1) . ": {$ip}:{$port}</h4>";
        
        // Probar URLs comunes de playlist
        $playlist_urls = [
            "http://{$ip}:{$port}/playlist.m3u8",
            "http://{$ip}:{$port}/playlist",
            "http://{$ip}:{$port}/channels.m3u8",
            "http://{$ip}/playlist.m3u8"
        ];
        
        foreach ($playlist_urls as $url) {
            echo "Probando: <code>{$url}</code> ... ";
            
            $test_context = stream_context_create([
                'http' => [
                    'timeout' => 5,
                    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                ]
            ]);
            
            $headers = @get_headers($url, 1, $test_context);
            
            if ($headers && strpos($headers[0], '200') !== false) {
                echo "✅ <strong>Accesible</strong><br>";
                
                // Intentar obtener contenido
                $content = @file_get_contents($url, false, $test_context);
                if ($content && strlen($content) > 0) {
                    echo "📄 Contenido encontrado (" . strlen($content) . " bytes)<br>";
                    
                    // Mostrar primeras líneas si parece ser M3U
                    if (strpos($content, '#EXTM3U') !== false || strpos($content, '#EXTINF') !== false) {
                        echo "🎵 <strong>Playlist M3U detectada!</strong><br>";
                        $lines = explode("\n", $content);
                        echo "<pre>" . htmlspecialchars(implode("\n", array_slice($lines, 0, 10))) . "</pre>";
                        if (count($lines) > 10) {
                            echo "<em>... y " . (count($lines) - 10) . " líneas más</em><br>";
                        }
                    }
                }
                break; // Si encontramos una URL que funciona, no probar las demás
            } else {
                echo "❌ No accesible<br>";
            }
        }
        echo "<br>";
    }
}

echo "<h2>📈 Estadísticas de la búsqueda</h2>";
echo "<ul>";
echo "<li><strong>País consultado:</strong> Honduras (HN)</li>";
echo "<li><strong>Tiempo de respuesta:</strong> {$response_time}ms</li>";
echo "<li><strong>Servidores encontrados:</strong> {$total_results}</li>";
echo "<li><strong>Tamaño de respuesta:</strong> " . strlen($response) . " bytes</li>";
echo "<li><strong>Fecha/Hora:</strong> " . date('Y-m-d H:i:s') . "</li>";
echo "</ul>";

// Mostrar información adicional de FOFA si está disponible
if (isset($data['size'])) {
    echo "<li><strong>Total en FOFA:</strong> " . $data['size'] . "</li>";
}
if (isset($data['page'])) {
    echo "<li><strong>Página:</strong> " . $data['page'] . "</li>";
}

echo "<h2>🔄 Próximos pasos</h2>";
if ($total_results > 0) {
    echo "<p>✅ ¡Excelente! Se encontraron servidores Astra en Honduras.</p>";
    echo "<p>Ahora puedes usar el script principal:</p>";
    echo "<code>http://tudominio.com/fofa7/list.php?HN</code>";
} else {
    echo "<p>🔍 No se encontraron servidores en Honduras. Puedes probar con otros países:</p>";
    echo "<ul>";
    echo "<li><code>?GT</code> - Guatemala</li>";
    echo "<li><code>?CR</code> - Costa Rica</li>";
    echo "<li><code>?SV</code> - El Salvador</li>";
    echo "<li><code>?NI</code> - Nicaragua</li>";
    echo "</ul>";
}
?>
