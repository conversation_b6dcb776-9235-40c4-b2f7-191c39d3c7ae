<?php
/**
 * Script PHP Autoactualizable para Canales FOFA
 * Uso: list.php?US, list.php?GT, list.php?CR, etc.
 * Autor: Sistema Automatizado
 * Portable - Funciona en cualquier hosting
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);
set_time_limit(300); // 5 minutos máximo

class FOFAChannelManager {
    private $fofa_key;
    private $cache_dir;
    private $cache_duration = 1800; // 30 minutos
    private $base_url;
    private $script_path;
    private $countries = [
        // Norteamérica
        'US' => 'United States',
        'CA' => 'Canada',
        'MX' => 'Mexico',

        // Centroamérica y Caribe
        'GT' => 'Guatemala',
        'BZ' => 'Belize',
        'SV' => 'El Salvador',
        'HN' => 'Honduras',
        'NI' => 'Nicaragua',
        'CR' => 'Costa Rica',
        'HN' => 'Honduras',
        'PA' => 'Panama',
        'CU' => 'Cuba',
        'JM' => 'Jamaica',
        'HT' => 'Haiti',
        'DO' => 'Dominican Republic',
        'PR' => 'Puerto Rico',
        'TT' => 'Trinidad and Tobago',

        // Sudamérica
        'CO' => 'Colombia',
        'VE' => 'Venezuela',
        'GY' => 'Guyana',
        'SR' => 'Suriname',
        'BR' => 'Brazil',
        'EC' => 'Ecuador',
        'PE' => 'Peru',
        'BO' => 'Bolivia',
        'PY' => 'Paraguay',
        'AR' => 'Argentina',
        'UY' => 'Uruguay',
        'CL' => 'Chile'
    ];

    public function __construct() {
        // Cargar configuración
        $config_file = dirname(__FILE__) . '/config.php';
        if (file_exists($config_file)) {
            $config = include $config_file;
            $this->fofa_key = $config['fofa_key'] ?? '';
            $this->cache_duration = $config['cache_duration'] ?? 1800;
        } else {
            // Fallback a API key por defecto (probablemente sin saldo)
            $this->fofa_key = '62b20e1e01319d0c005c580fbaf8c22f';
        }

        // Autodetectar la ubicación del script
        $this->script_path = isset($_SERVER['SCRIPT_NAME']) ? dirname($_SERVER['SCRIPT_NAME']) : '';
        $this->cache_dir = dirname(__FILE__) . '/cache/';

        // Construir URL base automáticamente (solo para web)
        if (php_sapi_name() !== 'cli') {
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
            $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
            $this->base_url = $protocol . '://' . $host . $this->script_path;
        } else {
            $this->base_url = 'http://localhost' . $this->script_path;
        }

        if (!file_exists($this->cache_dir)) {
            mkdir($this->cache_dir, 0755, true);
        }
    }
    
    /**
     * Procesa la petición principal
     */
    public function handleRequest() {
        $query_string = $_SERVER['QUERY_STRING'] ?? '';

        if (empty($query_string)) {
            $this->showUsage();
            return;
        }
        
        // Parsear parámetros
        if (strpos($query_string, '=') !== false) {
            // Formato: ?US=repretel6.m3u8
            parse_str($query_string, $params);
            $country = key($params);
            $channel = $params[$country];
            $this->serveChannel($country, $channel);
        } else {
            // Formato: ?US
            $country = $query_string;
            $this->listChannels($country);
        }
    }
    
    /**
     * Muestra el uso del script
     */
    private function showUsage() {
        header('Content-Type: text/html; charset=utf-8');
        $current_url = $this->base_url . '/list.php';

        echo "<h1>FOFA Channel Manager</h1>";
        echo "<p><strong>URL Base:</strong> <code>{$current_url}</code></p>";
        echo "<h2>Uso:</h2>";
        echo "<ul>";
        foreach ($this->countries as $code => $name) {
            echo "<li><a href='?{$code}' target='_blank'>{$current_url}?{$code}</a> - Listar canales de {$name}</li>";
        }
        echo "</ul>";
        echo "<h2>Ejemplo de canal específico:</h2>";
        echo "<p><code>{$current_url}?US=canal1.m3u8</code></p>";
        echo "<h2>Información del sistema:</h2>";
        echo "<ul>";
        echo "<li>Servidor: " . ($_SERVER['HTTP_HOST'] ?? 'CLI/Local') . "</li>";
        echo "<li>Ruta: " . $this->script_path . "</li>";
        echo "<li>Cache: " . (is_writable($this->cache_dir) ? '✅ Escribible' : '❌ Sin permisos') . "</li>";
        echo "<li>Hora actual: " . date('Y-m-d H:i:s') . "</li>";
        echo "<li>Modo: " . (php_sapi_name() === 'cli' ? 'CLI' : 'Web') . "</li>";
        echo "</ul>";
    }
    
    /**
     * Lista todos los canales disponibles para un país
     */
    private function listChannels($country) {
        if (!isset($this->countries[$country])) {
            http_response_code(400);
            echo "País no soportado: $country";
            return;
        }
        
        $channels = $this->getChannelsFromCache($country);
        
        if (empty($channels)) {
            $channels = $this->fetchChannelsFromFOFA($country);
            if (!empty($channels)) {
                $this->saveChannelsToCache($country, $channels);
            }
        }
        
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'country' => $country,
            'channels' => array_keys($channels),
            'total' => count($channels),
            'last_update' => date('Y-m-d H:i:s')
        ], JSON_PRETTY_PRINT);
    }
    
    /**
     * Sirve un canal específico
     */
    private function serveChannel($country, $channel_name) {
        $channels = $this->getChannelsFromCache($country);
        
        if (empty($channels)) {
            $channels = $this->fetchChannelsFromFOFA($country);
            if (!empty($channels)) {
                $this->saveChannelsToCache($country, $channels);
            }
        }
        
        // Buscar el canal
        $found_urls = [];
        foreach ($channels as $name => $urls) {
            if (stripos($name, $channel_name) !== false || $name === $channel_name) {
                $found_urls = $urls;
                break;
            }
        }
        
        if (empty($found_urls)) {
            http_response_code(404);
            echo "Canal no encontrado: $channel_name";
            return;
        }
        
        // Verificar URLs y devolver la primera funcional
        foreach ($found_urls as $url) {
            if ($this->checkUrlStatus($url)) {
                header('Content-Type: application/x-mpegURL');
                header('Location: ' . $url);
                exit;
            }
        }
        
        http_response_code(503);
        echo "Canal temporalmente no disponible: $channel_name";
    }
    
    /**
     * Obtiene canales desde FOFA
     */
    private function fetchChannelsFromFOFA($country) {
        $query = '"Astra Control Panel" && country="' . $country . '" && icon_hash="-463666563"';
        $query_base64 = base64_encode($query);
        
        $url = "https://fofa.info/api/v1/search/all?key={$this->fofa_key}&qbase64={$query_base64}&size=100";
        
        $context = stream_context_create([
            'http' => [
                'timeout' => 30,
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ]
        ]);
        
        $response = file_get_contents($url, false, $context);
        
        if ($response === false) {
            error_log("Error fetching from FOFA API");
            return [];
        }
        
        $data = json_decode($response, true);
        
        if (!$data || !isset($data['results'])) {
            error_log("Invalid FOFA API response");
            return [];
        }
        
        return $this->processAstraServers($data['results']);
    }
    
    /**
     * Procesa servidores Astra y extrae canales
     */
    private function processAstraServers($servers) {
        $channels = [];
        
        foreach ($servers as $server) {
            $ip = $server[0];
            $port = $server[1] ?? '8000';
            
            // Intentar obtener lista de canales del servidor Astra
            $playlist_urls = [
                "http://{$ip}:{$port}/playlist.m3u8",
                "http://{$ip}:{$port}/playlist",
                "http://{$ip}:{$port}/channels.m3u8",
                "http://{$ip}/playlist.m3u8",
                "http://{$ip}/channels.m3u8"
            ];
            
            foreach ($playlist_urls as $playlist_url) {
                $playlist_content = $this->fetchPlaylist($playlist_url);
                if ($playlist_content) {
                    $extracted_channels = $this->parseM3UPlaylist($playlist_content, $ip, $port);
                    $channels = array_merge_recursive($channels, $extracted_channels);
                    break;
                }
            }
        }
        
        return $channels;
    }
    
    /**
     * Obtiene contenido de playlist
     */
    private function fetchPlaylist($url) {
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ]
        ]);
        
        return @file_get_contents($url, false, $context);
    }
    
    /**
     * Parsea playlist M3U
     */
    private function parseM3UPlaylist($content, $ip, $port) {
        $channels = [];
        $lines = explode("\n", $content);
        $current_channel = null;
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            if (strpos($line, '#EXTINF:') === 0) {
                // Extraer nombre del canal
                if (preg_match('/#EXTINF:.*?,(.*)/', $line, $matches)) {
                    $current_channel = trim($matches[1]);
                }
            } elseif (!empty($line) && strpos($line, '#') !== 0 && $current_channel) {
                // URL del canal
                if (!filter_var($line, FILTER_VALIDATE_URL)) {
                    // URL relativa, construir URL completa
                    $line = "http://{$ip}:{$port}/" . ltrim($line, '/');
                }
                
                if (!isset($channels[$current_channel])) {
                    $channels[$current_channel] = [];
                }
                $channels[$current_channel][] = $line;
                $current_channel = null;
            }
        }
        
        return $channels;
    }
    
    /**
     * Verifica el estado de una URL
     */
    private function checkUrlStatus($url) {
        $context = stream_context_create([
            'http' => [
                'method' => 'HEAD',
                'timeout' => 5,
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ]
        ]);
        
        $headers = @get_headers($url, 1, $context);
        return $headers && strpos($headers[0], '200') !== false;
    }
    
    /**
     * Obtiene canales desde cache
     */
    private function getChannelsFromCache($country) {
        $cache_file = $this->cache_dir . "channels_{$country}.json";
        
        if (!file_exists($cache_file)) {
            return [];
        }
        
        $cache_time = filemtime($cache_file);
        if (time() - $cache_time > $this->cache_duration) {
            return [];
        }
        
        $content = file_get_contents($cache_file);
        return json_decode($content, true) ?: [];
    }
    
    /**
     * Guarda canales en cache
     */
    private function saveChannelsToCache($country, $channels) {
        $cache_file = $this->cache_dir . "channels_{$country}.json";
        file_put_contents($cache_file, json_encode($channels, JSON_PRETTY_PRINT));
    }
}

// Ejecutar el script
$manager = new FOFAChannelManager();
$manager->handleRequest();
?>
