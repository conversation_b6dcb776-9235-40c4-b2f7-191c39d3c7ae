# FOFA Channel Manager

Script PHP autoactualizable para obtener canales de servidores Astra usando la API de FOFA.

## Características

- ✅ Búsqueda automática de servidores Astra por país
- ✅ Cache inteligente con duración de 30 minutos
- ✅ Verificación automática del estado de URLs (HTTP 200)
- ✅ Auto-actualización periódica cada 30 minutos
- ✅ Soporte para múltiples países
- ✅ Failover automático entre IPs cuando un canal falla

## Instalación

1. Subir todos los archivos al hosting
2. Asegurar que el directorio tenga permisos de escritura
3. (Opcional) Configurar cron job para auto-actualización:
   ```bash
   chmod +x setup_cron.sh
   ./setup_cron.sh
   ```

## Uso

### Listar canales por país
```
http://rogsmediatv.xyz/mamones/fofa7/list.php?US
http://rogsmediatv.xyz/mamones/fofa7/list.php?GT
http://rogsmediatv.xyz/mamones/fofa7/list.php?CR
http://rogsmediatv.xyz/mamones/fofa7/list.php?MX
```

### Obtener canal específico
```
http://rogsmediatv.xyz/mamones/fofa7/list.php?US=repretel6.m3u8
http://rogsmediatv.xyz/mamones/fofa7/list.php?CR=rcnhd
```

### Países soportados
- `US` - Estados Unidos
- `GT` - Guatemala  
- `CR` - Costa Rica
- `MX` - México
- `CO` - Colombia
- `AR` - Argentina
- `CL` - Chile
- `PE` - Perú
- `EC` - Ecuador
- `VE` - Venezuela

## Funcionamiento

1. **Primera consulta**: El script consulta la API de FOFA buscando servidores Astra en el país especificado
2. **Extracción de canales**: Accede a cada servidor encontrado para obtener las listas M3U
3. **Cache**: Almacena los canales encontrados en cache por 30 minutos
4. **Verificación**: Verifica que las URLs respondan con HTTP 200
5. **Failover**: Si un canal falla, automáticamente prueba con otra IP de la lista
6. **Auto-actualización**: Cada 30 minutos verifica el estado y actualiza si es necesario

## Estructura de archivos

```
fofa7/
├── list.php           # Script principal
├── auto_update.php    # Script de auto-actualización
├── setup_cron.sh      # Configurador de cron job
├── cache/             # Directorio de cache (se crea automáticamente)
│   ├── channels_US.json
│   ├── channels_GT.json
│   └── auto_update.log
└── README.md          # Este archivo
```

## API de FOFA

El script utiliza la siguiente consulta para cada país:
```
"Astra Control Panel" && country="[PAÍS]" && icon_hash="-463666563"
```

## Logs

Los logs de auto-actualización se guardan en `cache/auto_update.log`

## Troubleshooting

- **Error 404**: Canal no encontrado en el cache
- **Error 503**: Canal temporalmente no disponible (todas las IPs fallan)
- **Cache vacío**: Ejecutar manualmente `auto_update.php?auto_update=1`

## Configuración avanzada

Para modificar la duración del cache o agregar más países, editar las variables en `list.php`:

```php
private $cache_duration = 1800; // 30 minutos en segundos
private $countries = [
    'NUEVO_PAIS' => 'Nombre del País'
];
```
