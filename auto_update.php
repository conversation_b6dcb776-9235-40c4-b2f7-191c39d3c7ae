<?php
/**
 * Script de Auto-actualización para verificación periódica
 * Se ejecuta cada 30 minutos para verificar el estado de los canales
 */

require_once 'list.php';

class AutoUpdater extends FOFAChannelManager {
    private $log_file;

    public function __construct() {
        parent::__construct();
        $this->log_file = dirname(__FILE__) . '/cache/auto_update.log';
    }
    
    /**
     * Ejecuta la verificación automática
     */
    public function runAutoUpdate() {
        $this->log("Iniciando verificación automática...");
        
        $countries = ['US', 'GT', 'CR', 'MX', 'CO', 'AR', 'CL', 'PE', 'EC', 'VE'];
        
        foreach ($countries as $country) {
            $this->log("Verificando país: $country");
            $this->updateCountryChannels($country);
        }
        
        $this->log("Verificación automática completada.");
    }
    
    /**
     * Actualiza canales de un país específico
     */
    private function updateCountryChannels($country) {
        $cache_file = dirname(__FILE__) . "/cache/channels_{$country}.json";
        
        // Verificar si existe cache
        if (!file_exists($cache_file)) {
            $this->log("No existe cache para $country, creando...");
            $this->fetchAndSaveChannels($country);
            return;
        }
        
        // Verificar edad del cache
        $cache_age = time() - filemtime($cache_file);
        if ($cache_age > 1800) { // 30 minutos
            $this->log("Cache de $country expirado, actualizando...");
            $this->fetchAndSaveChannels($country);
            return;
        }
        
        // Verificar estado de URLs en cache
        $channels = json_decode(file_get_contents($cache_file), true);
        $working_channels = 0;
        $total_urls = 0;
        
        foreach ($channels as $channel_name => $urls) {
            foreach ($urls as $url) {
                $total_urls++;
                if ($this->checkUrlStatus($url)) {
                    $working_channels++;
                }
            }
        }
        
        $success_rate = $total_urls > 0 ? ($working_channels / $total_urls) * 100 : 0;
        $this->log("País $country: $working_channels/$total_urls URLs funcionando ({$success_rate}%)");
        
        // Si menos del 50% funciona, actualizar desde FOFA
        if ($success_rate < 50) {
            $this->log("Tasa de éxito baja para $country, actualizando desde FOFA...");
            $this->fetchAndSaveChannels($country);
        }
    }
    
    /**
     * Obtiene y guarda canales desde FOFA
     */
    private function fetchAndSaveChannels($country) {
        $channels = $this->fetchChannelsFromFOFA($country);
        
        if (!empty($channels)) {
            $this->saveChannelsToCache($country, $channels);
            $this->log("Actualizados " . count($channels) . " canales para $country");
        } else {
            $this->log("No se pudieron obtener canales para $country");
        }
    }
    
    /**
     * Registra eventos en log
     */
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $log_entry = "[$timestamp] $message\n";
        file_put_contents($this->log_file, $log_entry, FILE_APPEND | LOCK_EX);
        echo $log_entry;
    }
    
    /**
     * Verifica el estado de una URL (override para logging)
     */
    protected function checkUrlStatus($url) {
        $context = stream_context_create([
            'http' => [
                'method' => 'HEAD',
                'timeout' => 3, // Timeout más corto para verificación masiva
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ]
        ]);
        
        $headers = @get_headers($url, 1, $context);
        $is_working = $headers && strpos($headers[0], '200') !== false;
        
        if (!$is_working) {
            $this->log("URL no funcional: $url");
        }
        
        return $is_working;
    }
}

// Si se ejecuta directamente
if (php_sapi_name() === 'cli' || isset($_GET['auto_update'])) {
    $updater = new AutoUpdater();
    $updater->runAutoUpdate();
} else {
    echo "Script de auto-actualización. Usar: ?auto_update=1 o ejecutar desde CLI";
}
?>
