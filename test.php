<?php
/**
 * Script de prueba para verificar el funcionamiento
 */

echo "<h1>FOFA Channel Manager - Pruebas</h1>";

// Verificar permisos de escritura
echo "<h2>1. Verificando permisos...</h2>";
if (!is_writable('.')) {
    echo "❌ El directorio no tiene permisos de escritura<br>";
} else {
    echo "✅ Permisos de escritura OK<br>";
}

// Crear directorio cache si no existe
if (!file_exists('cache')) {
    if (mkdir('cache', 0755, true)) {
        echo "✅ Directorio cache creado<br>";
    } else {
        echo "❌ No se pudo crear directorio cache<br>";
    }
} else {
    echo "✅ Directorio cache existe<br>";
}

// Verificar conectividad con FOFA
echo "<h2>2. Verificando conectividad con FOFA...</h2>";
$fofa_test_url = "https://fofa.info/api/v1/search/all?key=62b20e1e01319d0c005c580fbaf8c22f&qbase64=" . base64_encode('"test"') . "&size=1";

$context = stream_context_create([
    'http' => [
        'timeout' => 10,
        'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    ]
]);

$response = @file_get_contents($fofa_test_url, false, $context);

if ($response === false) {
    echo "❌ No se puede conectar con la API de FOFA<br>";
    echo "Error: " . error_get_last()['message'] . "<br>";
} else {
    $data = json_decode($response, true);
    if ($data && isset($data['results'])) {
        echo "✅ Conectividad con FOFA OK<br>";
        echo "Resultados de prueba: " . count($data['results']) . "<br>";
    } else {
        echo "❌ Respuesta inválida de FOFA<br>";
        echo "Respuesta: " . htmlspecialchars(substr($response, 0, 200)) . "...<br>";
    }
}

// Probar funciones básicas
echo "<h2>3. Probando funciones básicas...</h2>";

try {
    require_once 'list.php';
    echo "✅ Archivo list.php cargado correctamente<br>";
    
    $manager = new FOFAChannelManager();
    echo "✅ Clase FOFAChannelManager instanciada<br>";
    
} catch (Exception $e) {
    echo "❌ Error al cargar list.php: " . $e->getMessage() . "<br>";
}

// Enlaces de prueba
echo "<h2>4. Enlaces de prueba</h2>";
echo "<ul>";
echo "<li><a href='list.php' target='_blank'>Página principal</a></li>";
echo "<li><a href='list.php?US' target='_blank'>Canales de Estados Unidos</a></li>";
echo "<li><a href='list.php?CR' target='_blank'>Canales de Costa Rica</a></li>";
echo "<li><a href='auto_update.php?auto_update=1' target='_blank'>Ejecutar auto-actualización</a></li>";
echo "</ul>";

// Información del sistema
echo "<h2>5. Información del sistema</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Timezone: " . date_default_timezone_get() . "<br>";
echo "Current time: " . date('Y-m-d H:i:s') . "<br>";
echo "Memory limit: " . ini_get('memory_limit') . "<br>";
echo "Max execution time: " . ini_get('max_execution_time') . "<br>";

echo "<h2>6. Estructura de archivos</h2>";
$files = ['list.php', 'auto_update.php', 'setup_cron.sh', 'README.md', 'test.php'];
foreach ($files as $file) {
    if (file_exists($file)) {
        echo "✅ $file (" . number_format(filesize($file)) . " bytes)<br>";
    } else {
        echo "❌ $file (no encontrado)<br>";
    }
}

echo "<hr>";
echo "<p><strong>Si todas las verificaciones muestran ✅, el sistema está listo para usar.</strong></p>";
?>
